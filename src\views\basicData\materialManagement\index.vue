<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="物资类别" prop="materialCategoryId">
								<el-tree-select
									placeholder="请选择物资类别"
									v-model="state.queryForm.materialCategoryId"
									:data="treeCategory"
									check-strictly
									:render-after-expand="false"
									clearable
									:props="{ label: 'name', value: 'id' }"
								/>
							</el-form-item>
							<el-form-item label="物资标识" prop="materialIdentify">
								<el-input v-model="state.queryForm.materialIdentify" placeholder="请输入物资标识" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="物资名称" prop="materialName">
								<el-input v-model="state.queryForm.materialName" placeholder="请输入物资名称" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="userDialogRef.openDialog()">
							{{ $t('common.addBtn') }}
						</el-button>
						<el-button plain class="ml10" type="primary" @click="exportExcel"> 导出 </el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资类别" prop="categoryName" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="基本计量单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="启用状态" show-overflow-tooltip>
					<template #default="scope">
						<el-switch v-model="scope.row.status" @change="changeSwitch(scope.row)" :active-value="1" :inactive-value="0"></el-switch>
					</template>
				</el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 修改信息 -->
						<el-button icon="edit-pen" text type="primary" @click="userDialogRef.openDialog(scope.row.id)">
							{{ $t('common.editBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>

		<user-form ref="userDialogRef" @refresh="getDataList(false)" :categoryTree="treeCategory" />
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { materialCategoryTree, pageList, switchRow } from '/@/api/basicData/materialManagement';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件
const UserForm = defineAsyncComponent(() => import('./form.vue'));

const { t } = useI18n();

// 定义变量内容
const userDialogRef = ref();
const queryRef = ref();
const treeCategory = ref([]);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		materialCategoryId: '',
		materialName: '',
		materialIdentify: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

//获取物资类别tree
const getMaterialCategory = async () => {
	const { data } = await materialCategoryTree().catch(() => ({ data: [] }));
	treeCategory.value = data;
};

//表格内开关 (启用状态)
const changeSwitch = async (row: any) => {
	try {
		await switchRow({ id: row.id, status: row.status, materialCode: row.materialCode });
		useMessage().success(t('common.optSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		getDataList(false);
	}
};
// 导出excel
const exportExcel = async () => {
	try {
		await downBlobFile('/admin/materialCatalog/exportMaterialCatalog', state.queryForm, '物资目录.xlsx');
		useMessage().success('导出成功');
	} catch (err: any) {
		useMessage().error(err.msg || '导出失败');
	}
};
onMounted(() => {
	getMaterialCategory();
});
</script>
